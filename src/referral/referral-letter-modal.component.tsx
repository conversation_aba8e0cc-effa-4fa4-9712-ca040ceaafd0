import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Button, InlineLoading } from '@carbon/react';
import { restBaseUrl, openmrsFetch, showNotification, showToast } from '@openmrs/esm-framework';
import { useCurrentPatientUuid } from './use-current-patient.hook';
import { usePatientByUuid } from './use-patient-by-uuid.hook';
import styles from './referral-letter-modal.scss';
import useSWR, { mutate } from 'swr';

interface ReferralLetterModalProps {
  isOpen: boolean;
  onClose: () => void;
  referralData: {
    name: string;
    hospitalName: string;
    hospitalAddress: string;
    urgencyLevel: string;
    reasonForReferral: string;
    conditionSummary: string;
    typeOfReferral: string;
    referralSummary: string;
  };
  patientData?: {
    id?: string;
    name: string;
    age: number;
    gender: string;
    dateOfBirth: string;
    hospitalId: string;
  };
}

const ReferralLetterModal: React.FC<ReferralLetterModalProps> = ({ 
  isOpen, 
  onClose, 
  referralData,
  patientData // This prop is now optional and will be overridden by dynamic data
}) => {
  const { t } = useTranslation();
  const patientUuid = useCurrentPatientUuid();
  const [isSaving, setIsSaving] = useState(false);
  
  // Use the new usePatientByUuid hook to fetch current patient data by UUID
  const { patient, isLoading } = usePatientByUuid(patientUuid);

  // // Fetch latest vital signs for the patient
  // const { data: vitalsData, error: vitalsError, isLoading: vitalsLoading } = useSWR(
  //   // patientUuid ? `${restBaseUrl}/ws/rest/v1/obs?patient=${patientUuid}&concept=1114AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&v=custom:(concept:(display),value,obsDatetime)` : null,
  //   patientUuid ? `${restBaseUrl}/obs?patient=${patientUuid}&concept=1114AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&v=custom:(concept:(display),value,obsDatetime)` : null,
  //   openmrsFetch
  // );

  // console.log(vitalsData);


  // const { data: vitalsData, error: vitalsError, isLoading: vitalsLoading } = useSWR(
  //   patientUuid ? `${restBaseUrl}/obs?patient=${patientUuid}` : null,
  //   openmrsFetch,
  //   {
  //     revalidateOnFocus: false,     // Don't refetch when window gains focus
  //     revalidateOnReconnect: false, // Don't refetch when network reconnects
  //     refreshInterval: 0,           // Don't automatically refresh at intervals
  //     dedupingInterval: 0           // Don't dedupe requests (always make fresh requests)
  //   }
  // );


    // State for vitals
  const [vitalsData, setVitalsData] = useState<any>([]);
  const [vitalsError, setVitalsError] = useState<any>(null);
  const [vitalsLoading, setVitalsLoading] = useState(false);
  const [extractedVitals, setExtractedVitals] = useState<any>([]);

  useEffect(() => {
    if (!patientUuid) {
      setVitalsData(null);
      setVitalsError(null);
      setVitalsLoading(false);
      return;
    }
    setVitalsLoading(true);
    setVitalsError(null);
    console.log("hello no dr admin");
    
    // Set the X-TenantID cookie before making the fetch call
    // document.cookie = 'X-TenantID=public; path=/';
    // Check and log the base URL from the browser (window.location.origin)
    const browserBaseUrl = window.location.origin;
    if (browserBaseUrl.includes('localhost')) {
      console.log('Running on localhost:', browserBaseUrl);
    } else {
      console.log('Running on live URL:', browserBaseUrl);
    }
    fetch(`${browserBaseUrl}/openmrs/ws/rest/v1/obs?patient=${patientUuid}`, {
      headers: {
        'Authorization': 'Basic ' + btoa('admin:Admin123')
      }
    })
      .then(async (res) => {
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const data = await res.json();
        setVitalsData(data); // set array directly
        extractVitals(data)
        console.log(data, "vitals");
      })
      .catch((err) => {
        setVitalsError(err);
        setVitalsData(null);
      })
      .finally(() => setVitalsLoading(false));
  }, [patientUuid]);



  // useEffect(()=>{
  
  // },[vitalsData])

  // Fetch current user session information
  const { data: sessionData, error: sessionError, isLoading: sessionLoading } = useSWR(
    // `${restBaseUrl}/ws/rest/v1/session`,
    `${restBaseUrl}/session`,
    openmrsFetch
  );

  // Extract doctor name from session data
  const getDoctorName = () => {
    if (sessionData?.data && typeof sessionData.data === 'object' && 'user' in sessionData.data) {
      const user = (sessionData.data as any).user;
      if (user && typeof user === 'object' && 'person' in user) {
        const person = user.person;
        if (person && typeof person === 'object' && 'display' in person) {
          return person.display;
        }
      }
      // Fallback to user display if person.display is not available
      if (user && typeof user === 'object' && 'display' in user) {
        return user.display;
      }
    }
    return '';
  };

 

  const doctorName = getDoctorName();

  // Helper to extract vital values from OpenMRS obs data
  const extractVitals = (obsArray: any[]) => {
    if (!Array.isArray(obsArray) || obsArray.length === 0) return [];
    const vitals: Record<string, string> = {};
    obsArray.forEach(obs => {
      if (typeof obs.display === 'string') {
        const [name, ...rest] = obs.display.split(':');
        if (name && rest.length > 0) {
          vitals[name.trim()] = rest.join(':').trim();
        }
      }
    });
    setExtractedVitals(vitals)
    console.log(vitals,"desentitiake");
    console.log(Object.keys(vitals).length > 0  ? "true" : "false");
    
    return Object.keys(vitals).length > 0 ? vitals : null;
  };

  // const vitals = extractVitals(vitalsData);

  // Calculate age from birthDate
  const calculateAge = (birthDate: string) => {
    if (!birthDate) return 0;
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Use dynamic patient data if available, otherwise fall back to provided patientData
  const currentPatientData = patient ? {
    id: patient.id,
    name: patient.name?.[0] ? `${patient.name[0].given?.[0] || ''} ${patient.name[0].family || ''}`.trim() : 'Unknown Patient',
    age: calculateAge(patient.birthDate),
    gender: patient.gender || 'Unknown',
    dateOfBirth: formatDate(patient.birthDate),
    hospitalId: patient.identifier?.[0]?.value || 'N/A'
  } : {
    id: patientData?.id || patientUuid || '',
    name: patientData?.name || "Unknown Patient",
    age: patientData?.age || 0,
    gender: patientData?.gender || "Unknown",
    dateOfBirth: patientData?.dateOfBirth || "Unknown",
    hospitalId: patientData?.hospitalId || "N/A"
  };

  // Function to save patient transfer data
  const savePatientTransfer = async () => {
    if (!currentPatientData.id || !referralData.hospitalName) {
      showToast({
        title: t('error', 'Error'),
        kind: 'error',
        description: t('missingRequiredData', 'Missing required patient or referral data'),
      });
      return false;
    }

    try {
      const transferData = {
        patientId: currentPatientData.id,
        firstName: currentPatientData.name.split(' ')[0] || '',
        lastName: currentPatientData.name.split(' ').slice(1).join(' ') || '',
        encounterId: 1234,
        // encounterId: null,
        location: referralData.hospitalAddress || '',
        doctorName: referralData.name || '',
        transferReason: referralData.reasonForReferral || '',
        destinationHospital: referralData.hospitalName || ''
      };

      const response = await openmrsFetch(`${restBaseUrl}/patienttransfer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(transferData),
      });

      if (response.ok) {
        showToast({
          title: t('success', 'Success'),
          kind: 'success',
          description: t('patientTransferSaved', 'Patient transfer record saved successfully'),
        });
        console.log(response);
        return true;
      } else {
      console.log(response);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error saving patient transfer:', error);
      showToast({
        title: t('error', 'Error'),
        kind: 'error',
        description: t('patientTransferSaveError', 'Failed to save patient transfer record. Please try again.'),
      });
      return false;
    }
  };

  const handlePrint = async () => {
    console.log("handlePrint");
    setIsSaving(true);
    
    
    // Save patient transfer data first
    const transferSaved = await savePatientTransfer();
    console.log(transferSaved);
    
    
    setIsSaving(false);
    
    if (transferSaved) {
      // Only proceed with printing if the transfer was saved successfully
      window.print();
    }
  };

  const currentDate = new Date().toLocaleDateString('en-US', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  // Show loading state while patient data is being fetched
  if (isLoading) {
    return (
      <Modal
        open={isOpen}
        onRequestClose={onClose}
        modalHeading="Referral Letter"
        className={styles.referralLetterModal}
        size="lg"
      >
        <div className={styles.letterContent}>
          <p>Loading patient information...</p>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      open={isOpen}
      onRequestClose={onClose}
      modalHeading="Referral Letter"
      className={styles.referralLetterModal}
      size="lg"
        primaryButtonText="Print"
        secondaryButtonText="Exit"
        onRequestSubmit={handlePrint}
    >
      <div className={styles.letterContent}>
        <div className={styles.letterHeader}>
          <h2>Medical Referral Letter</h2>
          <p><strong>Date:</strong> {currentDate}</p>
        </div>

        <div className={styles.letterBody}>
          <div className={styles.recipientInfo}>
            <p><strong>[Receiving Doctor's Name]:</strong> {referralData.name}</p>
            <p><strong>[Receiving Hospital Name]:</strong> {referralData.hospitalName}</p>
            <p><strong>[Address]:</strong> {referralData.hospitalAddress}</p>
          </div>

          <p><strong>Subject: Patient Referral for Further Management - {currentPatientData.name}</strong></p>

          <p>Dear Dr. [{referralData.name || 'Receiving Doctor\'s Last Name'}],</p>

          <p>
            I am writing to refer {currentPatientData.name}, {currentPatientData.age} year old {currentPatientData.gender}, who has been under our care for [{referralData.conditionSummary || 'brief condition/symptoms summary'}]. {referralData.referralSummary || 'After evaluation and initial treatment, we believe further management at your facility is in the best interest of the patient.'}
          </p>

          <div className={styles.patientDetails}>
            <h4>Patient Details:</h4>
            <ul>
              <li><strong>Name:</strong> {currentPatientData.name}</li>
              <li><strong>Age:</strong> {currentPatientData.age}</li>
              <li><strong>Gender:</strong> {currentPatientData.gender}</li>
              <li><strong>Date of Birth:</strong> {currentPatientData.dateOfBirth}</li>
              <li><strong>Hospital ID:</strong> {currentPatientData.hospitalId}</li>
            </ul>
          </div>

          <div className={styles.medicalInfo}>
            <h4>Medical Information:</h4>
            <ul>
              <li><strong>Diagnosis:</strong> {referralData.conditionSummary || '[Preliminary/Confirmed Diagnosis]'}</li>
              <li><strong>Presenting Complaints:</strong> {referralData.conditionSummary || '[Brief overview]'}</li>
              <li><strong>Type of Referral:</strong> {referralData.typeOfReferral || '[Type of referral]'}</li>
              <li><strong>Reason for Referral:</strong> {referralData.reasonForReferral || '[e.g., need for specialized care, surgery, advanced diagnostics, etc.]'}</li>
              <li><strong>Referral Summary:</strong> {referralData.referralSummary || '[Referral summary]'}</li>
            </ul>
          </div>

          <div className={styles.vitalSigns}>
            <h4>Vital Signs:</h4>
            <div className={styles.vitalsGrid}>
              {vitalsLoading ? (
                <div>Loading vitals...</div>
              ) : vitalsError ? (
                <div>Error loading vitals.</div>
              ) : Array.isArray(vitalsData?.results) && vitalsData.results.length > 0 ? (
                vitalsData.results.map((items) => (
                  <div key={items.uuid} className={styles.vitalItem}>
                    <span>{items.display}</span>
                  </div>
                ))
              ) : (
                <div>No vitals recorded yet.</div>
              )}
            </div>
          </div>

          <p>
            Please feel free to contact us for any further clarification. We appreciate your attention to this referral and your 
            continued support in ensuring the best care for the patient.
          </p>

          <div className={styles.letterClosing}>
            <p>Thank you for your cooperation.</p>
            <p>Sincerely,</p>
            <p>{doctorName}</p>
          </div>
        </div>
      </div>

      {/* <div className={styles.modalActions}>
        <Button kind="secondary" onClick={onClose} disabled={isSaving}>
          Exit
        </Button>
        <Button 
          kind="primary" 
          onClick={handlePrint}
          disabled={isSaving || !currentPatientData.id}
        >
          {isSaving ? (
            <>
              <InlineLoading description="Saving..." />
              Saving & Print
            </>
          ) : (
            'Print'
          )}
        </Button>
      </div> */}
    </Modal>
  );
};

export default ReferralLetterModal; 