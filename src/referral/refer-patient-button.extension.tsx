import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MenuItem } from '@carbon/react';
import { Send } from '@carbon/react/icons';
import ReferralModal from './referral-modal.component';
import useSWR, { mutate } from 'swr';
import { restBaseUrl, openmrsFetch, showNotification, showToast } from '@openmrs/esm-framework';

export default function ReferPatientButton() {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const { data: sessionData, error: sessionError, isLoading: sessionLoading } = useSWR(
      // `${restBaseUrl}/ws/rest/v1/session`,
      `${restBaseUrl}/session`,
      openmrsFetch
    );
   // Extract user roles from session data
  const getUserRoles = () => {
    if (sessionData?.data && typeof sessionData.data === 'object' && 'user' in sessionData.data) {
      const user = (sessionData.data as any).user;
      if (user && typeof user === 'object' && 'roles' in user && Array.isArray(user.roles)) {
        return user.roles.map((role: any) => ({
          uuid: role.uuid || '',
          name: role.name || '',
          display: role.display || ''
        }));
      }
    }
    return [];
  };
  const userRoles = getUserRoles();

  return (
    <>
      <MenuItem
        label={t('referPatient', 'Refer Patient')}
        onClick={handleClick}
      >
      
      </MenuItem>
      {isModalOpen && (
        <ReferralModal 
          isOpen={isModalOpen} 
          onClose={handleCloseModal}
        />
      )}
    </>
  );
} 