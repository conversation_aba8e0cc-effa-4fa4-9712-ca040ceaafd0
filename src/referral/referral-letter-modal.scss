@use '@carbon/layout';
@use '@carbon/type';

.referralLetterModal {
  .letterContent {
    padding: layout.$spacing-06;
    background: white;
    font-family: 'IBM Plex Sans', Arial, sans-serif;
    line-height: 1.6;
    color: #161616;
  }

  .letterHeader {
    text-align: center;
    margin-bottom: layout.$spacing-06;
    
    h2 {
      @include type.type-style('heading-04');
      margin-bottom: layout.$spacing-03;
      color: #161616;
    }
    
    p {
      @include type.type-style('body-short-01');
      color: #525252;
    }
  }

  .letterBody {
    @include type.type-style('body-short-01');
    
    p {
      margin-bottom: layout.$spacing-04;
      text-align: justify;
    }
  }

  .recipientInfo {
    margin-bottom: layout.$spacing-05;
    
    p {
      margin-bottom: layout.$spacing-02;
    }
  }

  .patientDetails,
  .medicalInfo {
    margin: layout.$spacing-05 0;
    
    h4 {
      @include type.type-style('heading-compact-02');
      margin-bottom: layout.$spacing-03;
      color: #161616;
    }
    
    ul {
      margin-left: layout.$spacing-05;
      
      li {
        margin-bottom: layout.$spacing-02;
      }
    }
  }

  .vitalSigns {
    margin: layout.$spacing-05 0;
    
    h4 {
      @include type.type-style('heading-compact-02');
      margin-bottom: layout.$spacing-03;
      color: #161616;
    }
  }

  .vitalsGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: layout.$spacing-03;
    margin-left: layout.$spacing-05;
  }

  .vitalItem {
    display: flex;
    justify-content: space-between;
    padding: layout.$spacing-02 0;
    border-bottom: 1px solid #e0e0e0;
    
    span:first-child {
      font-weight: 500;
    }
  }

  .letterClosing {
    margin-top: layout.$spacing-06;
    
    p {
      margin-bottom: layout.$spacing-02;
    }
  }

  .modalActions {
    display: flex;
    gap: layout.$spacing-03;
    justify-content: flex-end;
    padding: layout.$spacing-04;
    border-top: 1px solid #e0e0e0;
    background: #f4f4f4;
  }
}

// Print styles
@media print {
  .referralLetterModal {
    .modalActions {
      display: none;
    }
    
    .letterContent {
      margin: 0;
      padding: 20px;
      box-shadow: none;
      border: none;
    }
  }
} 