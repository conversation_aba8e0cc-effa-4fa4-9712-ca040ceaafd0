import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  DataTable,
  Table,
  TableHead,
  TableRow,
  TableHeader,
  TableBody,
  TableCell,
  TableContainer,
  InlineLoading,
  Layer,
  Tile,
  Button,
  Modal,
} from '@carbon/react';
import { openmrsFetch, restBaseUrl } from '@openmrs/esm-framework';
import useSWR from 'swr';
import { XMLParser } from 'fast-xml-parser';
import styles from './referrals-table.scss';

interface PatientTransfer {
  id: string;
  uuid?: string;
  patientId: string;
  firstName: string;
  lastName: string;
  encounterId?: string | number;
  location: string;
  doctorName: string;
  transferReason: string;
  destinationHospital: string;
  dateCreated?: string;
  transferDate?: string;
  auditInfo?: {
    dateCreated?: string;
  };
}

interface ApiResponse {
  results?: PatientTransfer[];
  data?: PatientTransfer[];
}

function useReferrals() {
  const url = `${restBaseUrl}/patienttransfer`;
  
  const fetcher = async (url: string) => {
    try {
      const response = await openmrsFetch(url);
      console.log('Full response:', response); // Debug log
      console.log('Response data:', response.data); // Debug log
      return response.data; // Return the data property instead of the full response
    } catch (error) {
      console.error('Error fetching referrals:', error);
      throw error;
    }
  };
  
  const { data, error, isLoading } = useSWR<ApiResponse, Error>(url, fetcher);

  // Handle different response structures
  const referrals = data?.results || data?.data || [];

  return {
    referrals: Array.isArray(referrals) ? referrals : [],
    error: error,
    isLoading,
  };
}

async function fetchReferralDetails(uuid: string) {
   const browserBaseUrl = window.location.origin;
  const response = await fetch(`http://localhost/openmrs/ws/rest/v1/patienttransfer/${uuid}`);
  const xml = await response.text();
  const parser = new XMLParser();
  const json = parser.parse(xml);
  return json.object;
}

export default function TestDashboardshowComponent() {
  const { t } = useTranslation();
  const { referrals, error, isLoading } = useReferrals();
  const [selectedReferral, setSelectedReferral] = useState<any>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);

  const headers = [
    { key: 'uuid', header: 'ID' },
    { key: 'transferReason', header: 'Transfer Reason' },
    { key: 'actions', header: 'Actions' },
  ];

  const rows = referrals.map((referral) => ({
    id: referral.uuid,
    uuid: referral.uuid ? referral.uuid.slice(0, 8) + '...' : '',
    transferReason: referral.transferReason || t('notSpecified', 'Not specified'),
    actions: (
      <Button
        size="sm"
        onClick={async () => {
          setLoadingDetails(true);
          const details = await fetchReferralDetails(referral.uuid);
          setSelectedReferral(details);
          setModalOpen(true);
          setLoadingDetails(false);
        }}
      >
        {t('viewDetails', 'View Details')}
      </Button>
    ),
  }));

  if (isLoading) {
    return (
      <div className={styles.container}>
        <InlineLoading description={t('loadingReferrals', 'Loading referrals...')} />
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <Layer>
          <Tile className={styles.errorTile}>
            <h4>{t('errorLoadingReferrals', 'Error Loading Referrals')}</h4>
            <p>{t('errorMessage', 'Failed to load referrals data. Please try again later.')}</p>
            <p className={styles.errorDetails}>
              {error.message || t('unknownError', 'An unknown error occurred')}
            </p>
          </Tile>
        </Layer>
      </div>
    );
  }

  if (referrals.length === 0) {
    return (
      <div className={styles.container}>
        <Layer>
          <Tile className={styles.emptyState}>
            <h4>{t('noReferrals', 'No Referrals Found')}</h4>
            <p>{t('noReferralsMessage', 'There are no patient referrals to display at this time.')}</p>
          </Tile>
        </Layer>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3>{t('patientReferrals', 'Patient Referrals')}</h3>
        <p className={styles.subtitle}>
          {t('referralsCount', 'Showing {{count}} referral(s)', { count: referrals.length })}
        </p>
      </div>
      
      <DataTable rows={rows} headers={headers}>
        {({ rows, headers, getTableProps, getHeaderProps, getRowProps }) => (
          <TableContainer className={styles.tableContainer}>
            <Table {...getTableProps()}>
              <TableHead>
                <TableRow>
                  {headers.map((header) => (
                    <TableHeader key={header.key} {...getHeaderProps({ header })}>
                      {header.header}
                    </TableHeader>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {rows.map((row) => (
                  <TableRow key={row.id} {...getRowProps({ row })}>
                    {row.cells.map((cell) => (
                      <TableCell key={cell.id}>{cell.value}</TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </DataTable>
      <Modal open={modalOpen} onRequestClose={() => setModalOpen(false)} modalHeading="Referral Details">
        {loadingDetails ? (
          <div>Loading...</div>
        ) : selectedReferral ? (
          <div>
            <p><strong>Patient Name:</strong> {selectedReferral.firstName} {selectedReferral.lastName}</p>
            <p><strong>Patient ID:</strong> {selectedReferral.patientId}</p>
            <p><strong>Doctor Name:</strong> {selectedReferral.doctorName}</p>
            <p><strong>Destination Hospital:</strong> {selectedReferral.destinationHospital}</p>
            <p><strong>Location:</strong> {selectedReferral.location}</p>
            <p><strong>Transfer Reason:</strong> {selectedReferral.transferReason}</p>
            <p><strong>Date Created:</strong> {selectedReferral.dateCreated || selectedReferral.encounterId}</p>
            {/* Add more fields as needed */}
          </div>
        ) : (
          <div>No details available.</div>
        )}
      </Modal>
    </div>
  );
}

